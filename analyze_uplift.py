import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# Load the datasets
try:
    demelted_data = pd.read_csv('withOutliers_peter.csv')
    holidays = pd.read_csv('Holidays.csv')
except FileNotFoundError as e:
    print(f"Error loading data: {e}")
    exit()

# --- Data Preparation ---

# 1. Prepare demelted_data
# Filter for Key Selling Moments (KSM = 1)
ksm_data = demelted_data[demelted_data['KSM'] == 1].copy()

# Convert 'ABI Start' and 'ABI End' to datetime objects
ksm_data['ABI Start'] = pd.to_datetime(ksm_data['ABI Start'], errors='coerce')
ksm_data['ABI End'] = pd.to_datetime(ksm_data['ABI End'], errors='coerce')

# Drop rows where date conversion failed
ksm_data.dropna(subset=['ABI Start', 'ABI End'], inplace=True)

# 2. Prepare holidays data
# Convert 'date' to datetime objects and remove timezone information
holidays['date'] = pd.to_datetime(holidays['date'], errors='coerce').dt.tz_localize(None)

# Drop rows where date conversion failed
holidays.dropna(subset=['date'], inplace=True)
holidays.rename(columns={'bank_holiday_fr': 'Holiday', 'bank_holiday_uk': 'UK Holiday', 'date': 'holiday_date'}, inplace=True)


# --- Merge Data with 7-day window and overlap ---
# To consider promotions up to 7 days before a holiday and overlaps, we'll do a conditional merge.
# A cross join is one way to achieve this.
#NOT DOING THAT ANYMORE
ksm_data['key'] = 1
holidays['key'] = 1
merged_data = pd.merge(ksm_data, holidays, on='key').drop('key', axis=1)

# Define the 7-day window before the holiday
merged_data['window_start'] = merged_data['holiday_date']
merged_data['window_end'] = merged_data['holiday_date']

# Filter for promotions that overlap with the holiday window
# Overlap condition: (PromoStart <= WindowEnd) and (WindowStart <= PromoEnd)
promo_in_window = merged_data[
    (merged_data['ABI Start'] <= merged_data['window_end']) &
    (merged_data['window_start'] <= merged_data['ABI End'])
]

# --- Analysis ---
# Calculate the average uplift per holiday
avg_uplift_by_holiday = promo_in_window.groupby(['Holiday', 'UK Holiday'])['ABI MS Promo Uplift - rel'].mean().sort_values(ascending=False)

print("Average Uplift by Holiday (including 7 days prior and overlaps):")
print(avg_uplift_by_holiday)

# --- Export to CSV ---
avg_uplift_by_holiday.to_csv('holiday_uplift_analysis.csv', header=True)
print("\nAnalysis exported to holiday_uplift_analysis.csv")

# --- Visualization ---
# Reset index to use columns for plotting
plot_data = avg_uplift_by_holiday.reset_index()
plot_data['display_holiday'] = plot_data['Holiday'] + ' (' + plot_data['UK Holiday'] + ')'


plt.figure(figsize=(12, 8))
sns.barplot(x='ABI MS Promo Uplift - rel', y='display_holiday', data=plot_data, hue='display_holiday', palette='viridis', legend=False)
plt.title('Average "ABI MS Promo Uplift - rel" by Holiday (overlap)')
plt.xlabel('Average Uplift (rel)')
plt.ylabel('Holiday (FR & UK)')
plt.tight_layout()
plt.savefig('holiday_uplift_barchart_final.png')

print("\nBar chart saved as 'holiday_uplift_barchart_final.png'")